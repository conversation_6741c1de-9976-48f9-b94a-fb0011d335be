#!/usr/bin/env python3
"""
AI Text Enhancer for Vietnamese Chapter Content

This module provides AI-powered text enhancement using Google Gemini AI
to improve Vietnamese writing style and quality according to format.md guidelines.
"""

import os
import time
import logging
from typing import Optional, Dict, Any
from pathlib import Path
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GeminiTextEnhancer:
    """
    AI-powered text enhancer using Google Gemini for Vietnamese content improvement.
    """
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-2.5-flash"):
        """
        Initialize the Gemini AI enhancer.

        Args:
            api_key: Google AI Studio API key (if None, will try to get from environment)
            model_name: Gemini model to use for text enhancement
        """
        self.api_key = api_key or os.getenv('GOOGLE_AI_API_KEY')
        self.model_name = model_name
        self.model = None
        self.rate_limit_delay = 0.5  # Reduced delay for better performance
        self.max_retries = 3
        self.last_request_time = 0  # Track last request time for rate limiting

        # Performance optimizations
        self.content_cache = {}  # Cache for processed content
        self.max_cache_size = 100  # Maximum cache entries

        # Content processing settings
        self.max_content_length = 6000  # Max characters per chunk
        self.chunk_overlap = 200  # Overlap between chunks for context

        # Load formatting guidelines once and cache
        self.format_guidelines = self._load_format_guidelines()

        if not self.api_key:
            raise ValueError(
                "Google AI API key not found. Please set GOOGLE_AI_API_KEY environment variable "
                "or pass it directly to the constructor."
            )

        self._initialize_model()
    
    def _load_format_guidelines(self) -> str:
        """Load the format.md guidelines for AI processing."""
        format_file = Path("prompts/format.md")
        if format_file.exists():
            try:
                with open(format_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                logging.warning(f"Could not load format.md: {e}")
                return ""
        return ""
    
    def _initialize_model(self) -> None:
        """Initialize the Gemini AI model."""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            logging.info(f"Initialized Gemini model: {self.model_name}")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Gemini model: {e}")
    
    def _create_enhancement_prompt(self, chapter_name: str, chapter_content: str) -> str:
        """
        Create a detailed prompt for AI text enhancement.
        
        Args:
            chapter_name: The chapter title
            chapter_content: The chapter content to enhance
            
        Returns:
            A comprehensive prompt for AI processing
        """
        prompt = f"""
Bạn là một chuyên gia chỉnh sửa văn bản tiếng Việt chuyên về thể loại truyện tu tiên/cổ đại. 
Nhiệm vụ của bạn là cải thiện chất lượng văn phong của đoạn văn bản sau đây theo các hướng dẫn cụ thể.

## HƯỚNG DẪN CHỈNH SỬA:

{self.format_guidelines}

## NGUYÊN TẮC QUAN TRỌNG:
1. **KHÔNG THAY ĐỔI NỘI DUNG**: Chỉ cải thiện văn phong, không thêm bớt tình tiết
2. **GIỮ NGUYÊN TÊN RIÊNG**: Tất cả tên nhân vật, địa danh, công pháp phải giữ nguyên
3. **NHÂN XƯNG NHẤT QUÁN**: Sử dụng đúng cách xưng hô theo vai vế và mối quan hệ
4. **VĂN PHONG CỔ ĐIỂN**: Dùng từ ngữ Hán-Việt, tránh từ ngữ hiện đại
5. **GIỮ NGUYÊN CẤU TRÚC**: Không thay đổi số đoạn văn và cấu trúc chung

## CHƯƠNG CẦN CHỈNH SỬA:

**Tiêu đề:** {chapter_name}

**Nội dung:**
{chapter_content}

## YÊU CẦU XUẤT:
Hãy trả về CHỈ nội dung đã được chỉnh sửa, không bao gồm tiêu đề hay bất kỳ giải thích nào khác.
Nội dung phải được cải thiện về mặt văn phong nhưng giữ nguyên ý nghĩa và cấu trúc gốc.

**QUAN TRỌNG**: Phải trả về TOÀN BỘ nội dung đã được chỉnh sửa, không được bỏ sót hay cắt ngắn bất kỳ phần nào.
Nếu nội dung gốc dài, hãy đảm bảo chỉnh sửa hết tất cả các phần từ đầu đến cuối.
"""
        return prompt.strip()
    
    def _get_content_hash(self, content: str) -> str:
        """Generate a hash for content caching."""
        import hashlib
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def _manage_cache(self) -> None:
        """Manage cache size to prevent memory issues."""
        if len(self.content_cache) > self.max_cache_size:
            # Remove oldest entries (simple FIFO)
            oldest_keys = list(self.content_cache.keys())[:len(self.content_cache) - self.max_cache_size + 10]
            for key in oldest_keys:
                del self.content_cache[key]

    def _respect_rate_limit(self) -> None:
        """Ensure we respect API rate limits."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()

    def _split_content_into_chunks(self, content: str) -> list[str]:
        """Split large content into manageable chunks."""
        if len(content) <= self.max_content_length:
            return [content]

        chunks = []
        lines = content.split('\n')
        current_chunk = []
        current_length = 0

        for line in lines:
            line_length = len(line) + 1  # +1 for newline

            # If adding this line would exceed the limit, start a new chunk
            if current_length + line_length > self.max_content_length and current_chunk:
                chunks.append('\n'.join(current_chunk))

                # Start new chunk with overlap from previous chunk
                overlap_lines = current_chunk[-3:] if len(current_chunk) > 3 else current_chunk
                current_chunk = overlap_lines + [line]
                current_length = sum(len(l) + 1 for l in current_chunk)
            else:
                current_chunk.append(line)
                current_length += line_length

        # Add the last chunk
        if current_chunk:
            chunks.append('\n'.join(current_chunk))

        return chunks

    def _handle_api_error(self, error: Exception, attempt: int, chapter_name: str) -> bool:
        """Handle API errors and determine if we should retry."""
        error_msg = str(error).lower()

        if "finish_reason" in error_msg and "2" in error_msg:
            logging.warning(f"Content safety filter triggered for {chapter_name[:50]}...")
            return False  # Don't retry safety filter issues
        elif "quota" in error_msg or "rate limit" in error_msg:
            logging.warning(f"Rate limit hit for {chapter_name[:50]}...")
            time.sleep(self.rate_limit_delay * 3)
            return True  # Retry rate limit issues
        elif "timeout" in error_msg:
            logging.warning(f"Timeout for {chapter_name[:50]}...")
            time.sleep(self.rate_limit_delay * 2)
            return True  # Retry timeout issues
        else:
            logging.error(f"Unknown API error for {chapter_name[:50]}: {error}")
            return attempt < self.max_retries - 1  # Retry unknown errors

    def _enhance_chunk(self, chapter_name: str, chunk: str, chunk_index: int, total_chunks: int) -> Optional[str]:
        """Enhance a single chunk of content."""
        chunk_name = f"{chapter_name} (part {chunk_index + 1}/{total_chunks})"

        for attempt in range(self.max_retries):
            try:
                # Respect rate limits
                if attempt == 0:
                    self._respect_rate_limit()
                else:
                    time.sleep(self.rate_limit_delay * (2 ** attempt))

                logging.info(f"Enhancing chunk: {chunk_name} (attempt {attempt + 1})")

                prompt = self._create_enhancement_prompt(chapter_name, chunk)

                logging.info(f"Enhance with prompt {prompt}")
                
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,
                        max_output_tokens=8192,
                        top_p=0.8,
                        top_k=40
                    )
                )

                if response and response.text:
                    enhanced_chunk = response.text.strip()
                    logging.info(f"Successfully enhanced chunk: {chunk_name}")
                    return enhanced_chunk
                else:
                    logging.warning(f"Empty response for chunk: {chunk_name}")

            except Exception as e:
                if not self._handle_api_error(e, attempt, chunk_name):
                    logging.error(f"Unrecoverable error for chunk {chunk_name}: {e}")
                    return None

                if attempt == self.max_retries - 1:
                    logging.error(f"Failed to enhance chunk after {self.max_retries} attempts: {chunk_name}")
                    return None

        return None

    def enhance_text(self, chapter_name: str, chapter_content: str) -> Optional[str]:
        """
        Enhance Vietnamese text using Gemini AI with chunking and improved error handling.

        Args:
            chapter_name: The chapter title
            chapter_content: The chapter content to enhance

        Returns:
            Enhanced text or None if enhancement fails
        """
        if not self.model:
            logging.error("Gemini model not initialized")
            return None

        if not chapter_content.strip():
            logging.warning("Empty chapter content provided")
            return chapter_content

        # Check cache first
        content_hash = self._get_content_hash(chapter_content)
        if content_hash in self.content_cache:
            logging.info(f"Using cached result for chapter: {chapter_name[:50]}...")
            return self.content_cache[content_hash]

        # Split content into chunks if it's too large
        chunks = self._split_content_into_chunks(chapter_content)

        if len(chunks) == 1:
            # Single chunk - process normally
            enhanced_text = self._enhance_single_content(chapter_name, chapter_content)
        else:
            # Multiple chunks - process each and combine
            logging.info(f"Processing {len(chunks)} chunks for chapter: {chapter_name[:50]}...")
            enhanced_chunks = []

            for i, chunk in enumerate(chunks):
                enhanced_chunk = self._enhance_chunk(chapter_name, chunk, i, len(chunks))
                if enhanced_chunk:
                    enhanced_chunks.append(enhanced_chunk)
                else:
                    logging.error(f"Failed to enhance chunk {i + 1}/{len(chunks)} for {chapter_name[:50]}")
                    return None  # If any chunk fails, fail the whole process

            # Combine enhanced chunks
            enhanced_text = '\n\n'.join(enhanced_chunks)

            # Remove potential duplicate content at chunk boundaries
            enhanced_text = self._clean_chunk_boundaries(enhanced_text)

        if enhanced_text:
            # Cache the result
            self.content_cache[content_hash] = enhanced_text
            self._manage_cache()

            original_length = len(chapter_content.strip())
            enhanced_length = len(enhanced_text)
            logging.info(f"Successfully enhanced chapter: {chapter_name[:50]}... "
                       f"({original_length} -> {enhanced_length} chars)")
            return enhanced_text

        return None

    def _enhance_single_content(self, chapter_name: str, chapter_content: str) -> Optional[str]:
        """Enhance content as a single piece."""
        for attempt in range(self.max_retries):
            try:
                # Respect rate limits
                if attempt == 0:
                    self._respect_rate_limit()
                else:
                    time.sleep(self.rate_limit_delay * (2 ** attempt))

                logging.info(f"Enhancing text for chapter: {chapter_name[:50]}... (attempt {attempt + 1})")

                prompt = self._create_enhancement_prompt(chapter_name, chapter_content)

                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,
                        max_output_tokens=8192,
                        top_p=0.8,
                        top_k=40
                    )
                )

                if response and response.text:
                    enhanced_text = response.text.strip()

                    # Validate content quality
                    if self._validate_enhanced_content(enhanced_text, chapter_content, chapter_name):
                        return enhanced_text
                    elif attempt < self.max_retries - 1:
                        logging.info(f"Retrying due to validation failure for {chapter_name[:50]}...")
                        continue
                    else:
                        logging.error(f"All attempts failed validation for {chapter_name[:50]}...")
                        return None
                else:
                    logging.warning(f"Empty response from Gemini for chapter: {chapter_name[:50]}...")

            except Exception as e:
                if not self._handle_api_error(e, attempt, chapter_name):
                    return None

                if attempt == self.max_retries - 1:
                    logging.error(f"Failed to enhance chapter after {self.max_retries} attempts: {chapter_name}")
                    return None

        return None

    def _validate_enhanced_content(self, enhanced_text: str, original_content: str, chapter_name: str) -> bool:
        """Validate that enhanced content meets quality standards."""
        original_length = len(original_content.strip())
        enhanced_length = len(enhanced_text)

        # Check for truncation indicators
        is_truncated = (
            enhanced_length < original_length * 0.7 or  # Significant size reduction
            enhanced_text.endswith('"') and not original_content.strip().endswith('"') or  # Ends with quote unexpectedly
            enhanced_text.endswith('...') or  # Ends with ellipsis
            len(enhanced_text.split('\n')) < len(original_content.split('\n')) * 0.5 or  # Too few lines
            enhanced_length < 50  # Too short to be meaningful
        )

        if is_truncated:
            logging.warning(f"Enhanced text appears truncated for {chapter_name[:50]}... "
                          f"(Original: {original_length} chars, Enhanced: {enhanced_length} chars)")
            return False

        return True

    def _clean_chunk_boundaries(self, combined_text: str) -> str:
        """Clean up potential duplicate content at chunk boundaries."""
        # This is a simple implementation - could be made more sophisticated
        lines = combined_text.split('\n')
        cleaned_lines = []

        for i, line in enumerate(lines):
            # Skip empty lines that might be duplicated
            if not line.strip():
                if i == 0 or not lines[i-1].strip():
                    continue

            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)
    
    def is_available(self) -> bool:
        """
        Check if the AI enhancer is available and properly configured.
        
        Returns:
            True if the enhancer is ready to use, False otherwise
        """
        return self.model is not None and self.api_key is not None
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the AI enhancer.

        Returns:
            Dictionary containing status information
        """
        return {
            "model_name": self.model_name,
            "api_key_configured": bool(self.api_key),
            "model_initialized": self.model is not None,
            "format_guidelines_loaded": bool(self.format_guidelines),
            "rate_limit_delay": self.rate_limit_delay,
            "max_retries": self.max_retries,
            "cache_size": len(self.content_cache),
            "max_cache_size": self.max_cache_size
        }

    def clear_cache(self) -> None:
        """Clear the content cache."""
        self.content_cache.clear()
        logging.info("Content cache cleared")


def test_enhancer():
    """Test function for the AI enhancer."""
    try:
        enhancer = GeminiTextEnhancer()
        
        if not enhancer.is_available():
            print("❌ AI Enhancer not available. Check your API key configuration.")
            return
        
        print("✅ AI Enhancer initialized successfully")
        print("Status:", enhancer.get_status())
        
        # Test with sample text
        test_chapter = "Test Chapter"
        test_content = """Lâm Bạch không biết Cố Thanh Hàn có bao nhiêu cảm động, hắn chỉ cảm thấy Tô Mị người thị nữ này thật sự là quá không hiểu chuyện! Sáng sớm, lại một lần bị liên lụy đến vết thương về sau, Lâm Bạch kêu dừng Tô Mị vì hắn cởi áo hành vi."""
        
        print(f"\n🔄 Testing enhancement...")
        enhanced = enhancer.enhance_text(test_chapter, test_content)
        
        if enhanced:
            print("✅ Enhancement successful!")
            print(f"Original: {test_content[:100]}...")
            print(f"Enhanced: {enhanced[:100]}...")
        else:
            print("❌ Enhancement failed")
            
    except Exception as e:
        print(f"❌ Error testing enhancer: {e}")


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    test_enhancer()
