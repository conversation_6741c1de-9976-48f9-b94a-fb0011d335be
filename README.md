# GetContent: Vietnamese Text Processing and AI Enhancement Suite

A comprehensive suite of Python tools designed to process, convert, and enhance Vietnamese text files. This project provides scripts for converting Excel data into structured text files and for batch-enhancing text quality using Google Gemini AI with a focus on Vietnamese literary style.

## Features

- ✅ **Vietnamese Text Support**: Full UTF-8 encoding for proper handling of Vietnamese characters.
- 🤖 **AI-Powered Text Enhancement**: Integrates Google Gemini AI to improve Vietnamese writing style, grammar, and vocabulary, with a focus on converting modern text to a classical literary style.
- 批量 **Batch Processing**: Process single files or entire directories of `.md` and `.txt` files.
- 📁 **Structure Preservation**: Maintains the original directory structure when processing folders.
- 엑셀 **Excel to Text Conversion**: Converts Excel files with chapter data into individual `.txt` or `.md` files.
- 🔧 **Flexible Configuration**: Customizable output paths, filename suffixes, and processing options.
- 🛡️ **Robust Error Handling**: Graceful fallbacks for AI failures, API rate limit handling, and detailed error reporting.
- 📊 **Progress Tracking**: Real-time progress bars and summary statistics for processing jobs.
- ✍️ **Filename Sanitization**: Automatically cleans and handles invalid or duplicate filenames.
- CLI **CLI Interface**: Easy-to-use command-line interfaces for all tools.
- 跨平台 **Cross-Platform**: Works on Windows, macOS, and Linux.

## Requirements

- Python 3.7+
- A Google AI Studio API key (for AI enhancement feature)
- Required Python packages listed in `requirements.txt`.

## Installation

1.  Clone or download this project.
2.  Install the required dependencies:
    ```bash
    pip install -r requirements.txt
    ```

## AI Enhancement Setup

To use the AI-powered text enhancement features, you need to configure your Google AI API key.

### 1. Get Your Google AI API Key

1.  Visit [Google AI Studio](https://makersuite.google.com/app/apikey).
2.  Sign in with your Google account.
3.  Click "Create API Key" and copy the generated key.

### 2. Configure Environment Variables

The recommended way is to use a `.env` file.

1.  Copy the example file:
    ```bash
    cp .env.example .env
    ```
2.  Edit the `.env` file and add your API key:
    ```
    GOOGLE_AI_API_KEY=your_actual_api_key_here
    ```

Alternatively, you can set the environment variable directly in your shell, but this is not recommended for security reasons.

## Usage

This project includes two main tools.

### Tool 1: `excel_to_files.py` - Excel to Text Converter

This script converts an Excel file containing chapter data into individual text files.

#### Excel File Format

Your Excel file must contain at least two columns: `chapters` (for titles) and `chapter-content` (for the text).

| chapters | chapter-content |
| :--- | :--- |
| Chương 1: Giới thiệu | Nội dung chương 1 với tiếng Việt... |
| Chương 2: Lịch sử | Nội dung chương 2 với tiếng Việt... |

#### Basic Usage

```bash
# Convert an Excel file to .txt files
python excel_to_files.py your_excel_file.xlsx
```

#### Advanced Usage

```bash
# Specify output directory and Markdown format
python excel_to_files.py your_excel_file.xlsx --output-dir my_chapters --format md

# Enable AI text enhancement (requires Markdown format)
python excel_to_files.py your_excel_file.xlsx --format md --ai-enhance

# Combine all options
python excel_to_files.py chapters.xlsx -o chapters_md --format md --ai-enhance -v
```

#### Command Line Options

| Option | Short | Description | Default |
| :--- | :--- | :--- | :--- |
| `--output-dir` | `-o` | Output directory for generated files | `output` |
| `--format` | `-f` | Output format: `txt` or `md` | `txt` |
| `--ai-enhance` | `--ai` | Enable AI text enhancement (Markdown only) | `False` |
| `--encoding` | | Text encoding for output files | `utf-8` |
| `--verbose` | `-v` | Enable verbose logging | `False` |

---

### Tool 2: `text_enhancer.py` - Batch Text File Enhancer

A powerful script that enhances the writing style and quality of existing `.md` or `.txt` files in batch.

#### Basic Usage

```bash
# Process a single file
python text_enhancer.py input.md

# Process all files in a directory
python text_enhancer.py input_folder/

# Specify a custom output directory
python text_enhancer.py input_folder/ -o enhanced_output/
```

#### Advanced Options

```bash
# Disable AI enhancement (performs basic processing only)
python text_enhancer.py input_folder/ --no-ai

# Use a custom filename suffix
python text_enhancer.py input_folder/ --suffix _improved

# Don't preserve directory structure (flattens output)
python text_enhancer.py input_folder/ --flat

# Enable verbose logging
python text_enhancer.py input_folder/ -v
```

#### Command Line Arguments

| Argument | Description | Default |
| :--- | :--- | :--- |
| `input` | Input file or directory path | Required |
| `-o, --output` | Output directory | `enhanced_output` |
| `--no-ai` | Disable AI enhancement | False |
| `--suffix` | Suffix for enhanced filenames | `_enhanced` |
| `--flat` | Don't preserve directory structure | False |
| `-v, --verbose` | Enable verbose logging | False |

## AI Enhancement Details

### What the AI Does

When enabled, the AI enhancement feature processes `.md` files to:

-   **Improve Vietnamese Writing**: Enhances grammar, flow, and style according to the guidelines in `format.md`.
-   **Adopt a Classical/Ancient Style**: Converts modern Vietnamese into a more classical, literary style suitable for historical fiction.
-   **Standardize Terminology**: Ensures consistency in character titles and relationships (e.g., *ta, ngươi, hắn, nàng*).
-   **Enrich Vocabulary**: Introduces appropriate Hán-Việt terms to elevate the text.

### What the AI Preserves

The AI is designed to enhance style without altering the core substance:

-   ✅ Original plot, events, and content.
-   ✅ Character names, place names, and proper nouns.
-   ✅ Chapter structure and narrative flow.
-   ✅ Dialogue and core meaning.

### Fallback Behavior

If the AI enhancement fails for any reason (e.g., API error, rate limit), the system will gracefully fall back to using the original, un-enhanced content for the affected file and continue processing others.

## Output and File Handling

### Output Structure

-   **Default (Preserved)**: The output directory mirrors the input directory's structure.
-   **Flat (`--flat`)**: All processed files are saved directly in the output directory, without subfolders.

### Filename Handling

-   **Sanitization**: Invalid filename characters (`< > : " | ? * \ /`) are replaced with an underscore (`_`).
-   **Duplicate Names**: If a filename already exists, a numeric suffix is added (e.g., `chapter_1.md`, `chapter_1_2.md`).
-   **Suffix**: A suffix (default: `_enhanced`) is added to processed filenames.

## Performance Considerations

-   **Without AI**: Processing is very fast, typically less than a second per file.
-   **With AI**: Processing takes longer, around 2-10 seconds per file, depending on content length and API response times.
-   **Rate Limits**: The scripts have built-in exponential backoff to handle API rate limits gracefully.
-   **Memory Usage**: Files are processed individually to keep memory usage low.

## Troubleshooting

### Common Issues

1.  **Module Not Found / Import Error**:
    -   **Error**: `ModuleNotFoundError: No module named 'google.generativeai'`
    -   **Solution**: Ensure you have installed all dependencies with `pip install -r requirements.txt`.

2.  **Google AI API Key Not Found**:
    -   **Error**: `Error: Google AI API key not found`
    -   **Solution**: Make sure your `GOOGLE_AI_API_KEY` is correctly set in your `.env` file or as an environment variable.

3.  **AI Enhancement Not Working**:
    -   Ensure you are using `--format md` with the `excel_to_files.py` script.
    -   Ensure the `--ai-enhance` flag is present.
    -   Check for verbose logs (`-v`) for any specific error messages from the AI service.

4.  **Permission Errors**:
    -   **Solution**: Check that you have read permissions for the input files/directory and write permissions for the output directory.

### Getting Help

For any script, you can use the `--help` flag to see all available options and their descriptions.
```bash
python excel_to_files.py --help
python text_enhancer.py --help
```

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.
