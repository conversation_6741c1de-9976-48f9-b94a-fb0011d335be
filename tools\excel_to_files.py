#!/usr/bin/env python3
"""
Excel to Files Converter

This script reads an Excel file containing chapter names and content,
then creates individual text files for each chapter with proper Vietnamese text support.

Requirements:
- Excel file must have columns named "chapter" and "chapter-content"
- <PERSON>les Vietnamese text encoding properly
- Sanitizes filenames for cross-platform compatibility
- <PERSON><PERSON> duplicate chapter names
"""

import os
import re
import sys
import unicodedata
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pandas as pd
import click
from tqdm import tqdm
from ai_enhancer import GeminiTextEnhancer


class ExcelToFilesConverter:
    """Main converter class for processing Excel files to individual text files."""

    def __init__(self, output_dir: str = "output", file_extension: str = ".txt", enable_ai: bool = False):
        """
        Initialize the converter.

        Args:
            output_dir: Directory to save the generated files
            file_extension: File extension for output files (.txt or .md)
            enable_ai: Whether to enable AI text enhancement for .md files
        """
        self.output_dir = Path(output_dir)
        self.file_extension = file_extension
        self.chapter_counts: Dict[str, int] = {}
        self.enable_ai = enable_ai
        self.ai_enhancer = None

        # Initialize AI enhancer if requested and format is markdown
        if self.enable_ai and self.file_extension == ".md":
            try:
                self.ai_enhancer = GeminiTextEnhancer()
                if self.ai_enhancer.is_available():
                    print("🤖 AI text enhancement enabled for Markdown files")
                    logging.info("AI enhancer initialized successfully")
                else:
                    print("⚠️  AI enhancer not available, proceeding without AI enhancement")
                    self.ai_enhancer = None
            except Exception as e:
                print(f"⚠️  Failed to initialize AI enhancer: {e}")
                print("   Proceeding without AI enhancement")
                self.ai_enhancer = None
                logging.error(f"AI enhancer initialization failed: {e}")
        
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize a string to be safe for use as a filename.
        
        Args:
            filename: The original filename string
            
        Returns:
            A sanitized filename safe for cross-platform use
        """
        # First, normalize whitespace and remove control characters
        # Replace tabs, newlines, and other whitespace with single spaces
        sanitized = re.sub(r'\s+', ' ', filename)
        
        # Remove control characters (ASCII 0-31 and 127)
        sanitized = re.sub(r'[\x00-\x1f\x7f]', '', sanitized)
        
        # Remove or replace invalid characters for filenames
        # Invalid characters: < > : " | ? * \ /
        invalid_chars = r'[<>:"|?*\\/]'
        sanitized = re.sub(invalid_chars, '_', sanitized)
        
        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(' .')
        
        # Replace multiple consecutive underscores with single underscore
        sanitized = re.sub(r'_+', '_', sanitized)
        
        # Limit length to avoid filesystem issues (255 chars is common limit)
        max_length = 200  # Leave room for extension and potential numbering
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length].strip()
        
        # Ensure we don't have an empty filename
        if not sanitized:
            sanitized = "untitled"
            
        return sanitized
    
    def handle_duplicate_filename(self, base_filename: str) -> str:
        """
        Handle duplicate filenames by appending a number.
        
        Args:
            base_filename: The base filename (without extension)
            
        Returns:
            A unique filename
        """
        if base_filename not in self.chapter_counts:
            self.chapter_counts[base_filename] = 1
            return base_filename
        else:
            self.chapter_counts[base_filename] += 1
            return f"{base_filename}_{self.chapter_counts[base_filename]}"
    
    def read_excel_file(self, excel_path: str) -> pd.DataFrame:
        """
        Read the Excel file and validate required columns.
        
        Args:
            excel_path: Path to the Excel file
            
        Returns:
            DataFrame with the Excel data
            
        Raises:
            FileNotFoundError: If the Excel file doesn't exist
            ValueError: If required columns are missing
        """
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel file not found: {excel_path}")
        
        try:
            # Read Excel file
            df = pd.read_excel(excel_path, engine='openpyxl')
        except Exception as e:
            raise ValueError(f"Error reading Excel file: {str(e)}")
        
        # Check for required columns
        required_columns = ['chapters', 'chapter-content']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            available_columns = list(df.columns)
            raise ValueError(
                f"Missing required columns: {missing_columns}. "
                f"Available columns: {available_columns}"
            )
        
        return df
    
    def write_chapter_file(self, chapter_name: str, chapter_content: str, filename: str, ai_enhanced: bool = False) -> None:
        """
        Write a chapter to a text file with proper encoding.

        Args:
            chapter_name: The chapter title/name
            chapter_content: The chapter content
            filename: The filename to write to
            ai_enhanced: Whether the content was enhanced by AI
        """
        try:
            file_path = self.output_dir / f"{filename}{self.file_extension}"

            # Ensure the directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Prepare the content
            if self.file_extension == ".md":
                # Markdown format
                content = f"# {chapter_name}\n\n{chapter_content}"
            else:
                # Plain text format
                content = f"{chapter_name}\n{'=' * len(chapter_name)}\n\n{chapter_content}"

            # Write file with UTF-8 encoding to support Vietnamese text
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Show appropriate success message
            if ai_enhanced:
                print(f"✓ Created (AI Enhanced): {file_path}")
            else:
                print(f"✓ Created: {file_path}")

        except OSError as e:
            print(f"✗ Error writing file (invalid path/filename): {filename}")
            print(f"  Original chapter name: {chapter_name[:100]}...")
            print(f"  Error details: {str(e)}")
        except Exception as e:
            print(f"✗ Error writing {filename}: {str(e)}")
    
    def convert(self, excel_path: str) -> None:
        """
        Main conversion method.

        Args:
            excel_path: Path to the Excel file to convert
        """
        print(f"Starting conversion of: {excel_path}")
        print(f"Output directory: {self.output_dir}")
        print(f"File extension: {self.file_extension}")
        if self.ai_enhancer:
            print("🤖 AI text enhancement: ENABLED")
        print("-" * 50)

        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Read Excel file
        try:
            df = self.read_excel_file(excel_path)
            print(f"Successfully read Excel file with {len(df)} rows")
        except Exception as e:
            print(f"Error: {str(e)}")
            return

        # Process each row
        successful_conversions = 0
        ai_enhanced_count = 0
        errors = 0

        # Create progress bar if AI enhancement is enabled
        if self.ai_enhancer:
            print("\n🤖 Processing chapters with AI enhancement...")
            progress_bar = tqdm(df.iterrows(), total=len(df), desc="Converting chapters", unit="chapter")
        else:
            progress_bar = df.iterrows()

        for index, row in progress_bar:
            try:
                chapter_name = str(row['chapters']).strip()
                chapter_content = str(row['chapter-content']).strip()

                # Skip rows with missing data
                if not chapter_name or chapter_name == 'nan':
                    print(f"⚠ Skipping row {index + 1}: Missing chapter name")
                    continue

                if not chapter_content or chapter_content == 'nan':
                    print(f"⚠ Skipping row {index + 1}: Missing chapter content")
                    continue

                # Sanitize filename
                base_filename = self.sanitize_filename(chapter_name)

                # Handle duplicates
                unique_filename = self.handle_duplicate_filename(base_filename)

                # AI Enhancement for Markdown files
                ai_enhanced = False
                final_content = chapter_content

                if self.ai_enhancer and self.file_extension == ".md":
                    try:
                        enhanced_content = self.ai_enhancer.enhance_text(chapter_name, chapter_content)
                        if enhanced_content and enhanced_content.strip():
                            final_content = enhanced_content
                            ai_enhanced = True
                            ai_enhanced_count += 1
                        else:
                            print(f"⚠️  AI enhancement failed for: {chapter_name[:50]}... (using original)")
                    except Exception as e:
                        print(f"⚠️  AI enhancement error for {chapter_name[:50]}...: {e}")
                        logging.error(f"AI enhancement failed for chapter '{chapter_name}': {e}")

                # Write the file
                self.write_chapter_file(chapter_name, final_content, unique_filename, ai_enhanced)
                successful_conversions += 1

            except Exception as e:
                print(f"✗ Error processing row {index + 1}: {str(e)}")
                errors += 1

        # Close progress bar if it was created
        if self.ai_enhancer and hasattr(progress_bar, 'close'):
            progress_bar.close()

        # Summary
        print("-" * 50)
        print(f"Conversion completed!")
        print(f"✓ Successfully converted: {successful_conversions} chapters")
        if self.ai_enhancer:
            print(f"🤖 AI enhanced: {ai_enhanced_count} chapters")
        if errors > 0:
            print(f"✗ Errors encountered: {errors}")
        print(f"Files saved to: {self.output_dir.absolute()}")


@click.command()
@click.argument('excel_file', type=click.Path(exists=True))
@click.option('--output-dir', '-o', default='output',
              help='Output directory for generated files (default: output)')
@click.option('--format', '-f', type=click.Choice(['txt', 'md']), default='txt',
              help='Output file format: txt or md (default: txt)')
@click.option('--ai-enhance', '--ai', is_flag=True, default=False,
              help='Enable AI text enhancement for Markdown files (requires GOOGLE_AI_API_KEY)')
@click.option('--encoding', default='utf-8',
              help='Text encoding for output files (default: utf-8)')
@click.option('--verbose', '-v', is_flag=True, default=False,
              help='Enable verbose logging')
def main(excel_file: str, output_dir: str, format: str, ai_enhance: bool, encoding: str, verbose: bool):
    """
    Convert Excel file to individual text files with optional AI enhancement.

    EXCEL_FILE: Path to the Excel file containing chapter data.
    The Excel file must have columns named 'chapters' and 'chapter-content'.

    AI Enhancement:
    When --ai-enhance is used with --format md, the tool will use Google Gemini AI
    to improve Vietnamese writing style and quality. Requires GOOGLE_AI_API_KEY
    environment variable to be set.
    """
    # Set up logging
    if verbose:
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    else:
        logging.basicConfig(level=logging.WARNING)

    file_extension = f".{format}"

    # Validate AI enhancement options
    if ai_enhance and format != 'md':
        print("⚠️  AI enhancement is only available for Markdown format (--format md)")
        print("   Proceeding without AI enhancement...")
        ai_enhance = False

    converter = ExcelToFilesConverter(
        output_dir=output_dir,
        file_extension=file_extension,
        enable_ai=ai_enhance
    )

    try:
        converter.convert(excel_file)
    except KeyboardInterrupt:
        print("\nConversion interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
